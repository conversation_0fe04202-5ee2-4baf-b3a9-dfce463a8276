import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Rocket } from "lucide-react";
import heroImage from "@/assets/hero-domain-ai.jpg";

const Hero = () => {
  return (
    <section className="relative overflow-hidden bg-gradient-hero min-h-screen flex items-center">
      {/* Enhanced Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-accent/20 rounded-full blur-2xl animate-bounce"></div>

        {/* Additional floating elements */}
        <div className="absolute top-20 right-20 w-4 h-4 bg-primary/30 rounded-full animate-float"></div>
        <div className="absolute bottom-32 left-16 w-6 h-6 bg-secondary/40 rounded-full animate-float delay-500"></div>
        <div className="absolute top-40 left-1/3 w-3 h-3 bg-accent/50 rounded-full animate-float delay-1000"></div>

        {/* Grid pattern overlay */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(124,58,237,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(124,58,237,0.03)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 py-12 sm:py-20 lg:py-32 relative z-10">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          <div className="space-y-6 lg:space-y-8 text-center lg:text-left">
            {/* Enhanced Badge */}
            <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary/10 via-primary/5 to-secondary/10 rounded-full border border-primary/20 animate-fade-in shadow-lg backdrop-blur-sm">
              <Sparkles className="h-4 w-4 text-primary animate-pulse" />
              <span className="text-sm font-medium text-primary">AI-Powered Domain Intelligence</span>
              <div className="w-2 h-2 bg-primary rounded-full animate-ping"></div>
            </div>

            <div className="space-y-4 lg:space-y-6">
              <h1 className="text-3xl sm:text-4xl lg:text-6xl xl:text-7xl font-bold leading-tight animate-slide-up">
                <span className="text-foreground block">
                  Spot, Evaluate,
                </span>
                <span className="text-foreground block">
                  Acquire, Resell
                </span>
              </h1>
              <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto lg:mx-0 animate-slide-up delay-200">
                The complete AI-powered platform for domain investors. Discover valuable domains,
                evaluate them with precision, and connect with buyers to maximize your ROI.
              </p>
            </div>

            {/* Enhanced CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start animate-slide-up delay-300">
              <Button
                size="lg"
                className="bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300 group relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                <Star className="mr-2 h-4 w-4 group-hover:rotate-12 transition-transform" />
                Start Free Trial
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="group hover:bg-primary/5 transition-all duration-300 border-2 hover:border-primary/30 hover:shadow-md"
              >
                <Play className="mr-2 h-4 w-4 group-hover:scale-110 transition-transform" />
                Watch Demo
                <div className="ml-2 w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              </Button>
            </div>

            {/* Enhanced Stats */}
            <div className="grid grid-cols-3 gap-4 sm:gap-8 pt-6 lg:pt-8 animate-slide-up delay-400">
              <div className="text-center lg:text-left group cursor-pointer p-4 rounded-xl hover:bg-white/5 transition-all duration-300">
                <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground group-hover:scale-110 transition-transform">
                  50M+
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground group-hover:text-foreground/80 transition-colors">Domains Analyzed</div>
                <div className="w-full h-1 bg-primary/20 rounded-full mt-2 overflow-hidden">
                  <div className="h-full bg-gradient-primary w-0 group-hover:w-full transition-all duration-1000 delay-200"></div>
                </div>
              </div>
              <div className="text-center lg:text-left group cursor-pointer p-4 rounded-xl hover:bg-white/5 transition-all duration-300">
                <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground group-hover:scale-110 transition-transform">
                  98%
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground group-hover:text-foreground/80 transition-colors">AI Accuracy</div>
                <div className="w-full h-1 bg-primary/20 rounded-full mt-2 overflow-hidden">
                  <div className="h-full bg-gradient-primary w-0 group-hover:w-[98%] transition-all duration-1000 delay-200"></div>
                </div>
              </div>
              <div className="text-center lg:text-left group cursor-pointer p-4 rounded-xl hover:bg-white/5 transition-all duration-300">
                <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground group-hover:scale-110 transition-transform">
                  $2.4M
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground group-hover:text-foreground/80 transition-colors">Total Sales</div>
                <div className="w-full h-1 bg-primary/20 rounded-full mt-2 overflow-hidden">
                  <div className="h-full bg-gradient-primary w-0 group-hover:w-full transition-all duration-1000 delay-200"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Hero Image */}
          <div className="relative order-first lg:order-last animate-slide-up delay-500">
            <div className="relative rounded-2xl overflow-hidden shadow-glow group">
              <img
                src={heroImage}
                alt="AI-powered domain analysis dashboard"
                className="w-full h-auto object-cover group-hover:scale-105 transition-transform duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-secondary/10 rounded-2xl opacity-30 group-hover:opacity-10 transition-opacity duration-300"></div>

              {/* Enhanced Floating Elements */}
              <div className="absolute top-4 right-4 bg-white/95 backdrop-blur-md rounded-xl p-3 shadow-xl animate-float border border-green-200">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-600">+25% ROI</span>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                </div>
              </div>

              <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-md rounded-xl p-3 shadow-xl animate-float delay-1000 border border-blue-200">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-600">AI Powered</span>
                  <Sparkles className="h-3 w-3 text-blue-400 animate-pulse" />
                </div>
              </div>

              <div className="absolute top-1/2 right-8 bg-white/95 backdrop-blur-md rounded-xl p-3 shadow-xl animate-float delay-500 border border-purple-200">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium text-purple-600">Secure</span>
                </div>
              </div>
            </div>

            {/* Enhanced Background Decoration */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-primary/30 to-secondary/20 rounded-full blur-xl animate-pulse"></div>
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-tr from-secondary/30 to-primary/20 rounded-full blur-xl animate-pulse delay-500"></div>
            <div className="absolute top-1/2 -left-8 w-16 h-16 bg-accent/20 rounded-full blur-lg animate-pulse delay-1000"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;