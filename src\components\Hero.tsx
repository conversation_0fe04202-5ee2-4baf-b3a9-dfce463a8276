import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Rocket } from "lucide-react";
import heroImage from "@/assets/hero-domain-ai.jpg";

const Hero = () => {
  return (
    <section className="relative overflow-hidden min-h-[75vh] flex items-center" style={{
      background: 'linear-gradient(135deg, #365eeb 0%, #4e77eb 25%, #40b6ef 75%, #040e22 100%)'
    }}>
      {/* Enhanced Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* White wavy threads */}
        <div className="absolute inset-0">
          <svg className="absolute top-0 left-0 w-full h-full opacity-20" viewBox="0 0 1200 800" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0,200 Q300,100 600,200 T1200,200" stroke="white" strokeWidth="2" fill="none" className="animate-pulse">
              <animate attributeName="d" dur="8s" repeatCount="indefinite" values="M0,200 Q300,100 600,200 T1200,200;M0,250 Q300,150 600,250 T1200,250;M0,200 Q300,100 600,200 T1200,200" />
            </path>
            <path d="M0,400 Q300,300 600,400 T1200,400" stroke="white" strokeWidth="1.5" fill="none" className="animate-pulse" style={{ animationDelay: '2s' }}>
              <animate attributeName="d" dur="10s" repeatCount="indefinite" values="M0,400 Q300,300 600,400 T1200,400;M0,450 Q300,350 600,450 T1200,450;M0,400 Q300,300 600,400 T1200,400" />
            </path>
            <path d="M0,600 Q300,500 600,600 T1200,600" stroke="white" strokeWidth="1" fill="none" className="animate-pulse" style={{ animationDelay: '4s' }}>
              <animate attributeName="d" dur="12s" repeatCount="indefinite" values="M0,600 Q300,500 600,600 T1200,600;M0,650 Q300,550 600,650 T1200,650;M0,600 Q300,500 600,600 T1200,600" />
            </path>
          </svg>
        </div>

        {/* Subtle floating elements */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-48 h-48 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-white/10 rounded-full blur-xl animate-bounce"></div>

        {/* Additional floating elements */}
        <div className="absolute top-20 right-20 w-3 h-3 bg-white/30 rounded-full animate-float"></div>
        <div className="absolute bottom-32 left-16 w-4 h-4 bg-white/40 rounded-full animate-float delay-500"></div>
        <div className="absolute top-40 left-1/3 w-2 h-2 bg-white/50 rounded-full animate-float delay-1000"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 py-12 sm:py-20 lg:py-32 relative z-10">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          <div className="space-y-6 lg:space-y-8 text-center lg:text-left">
            {/* Enhanced Badge */}
            <div className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 rounded-full border border-white/20 animate-fade-in shadow-lg backdrop-blur-sm">
              <Sparkles className="h-4 w-4 text-white animate-pulse" />
              <span className="text-sm font-medium text-white">AI-Powered Domain Intelligence</span>
              <div className="w-2 h-2 bg-white rounded-full animate-ping"></div>
            </div>

            <div className="space-y-4 lg:space-y-6">
              <h1 className="text-3xl sm:text-4xl lg:text-6xl xl:text-7xl font-bold leading-tight animate-slide-up">
                <span className="text-white block">
                  Spot, Evaluate,
                </span>
                <span className="text-white block">
                  Acquire, Resell
                </span>
              </h1>
              <p className="text-lg sm:text-xl text-white/80 max-w-2xl mx-auto lg:mx-0 animate-slide-up delay-200">
                The complete AI-powered platform for domain investors. Discover valuable domains,
                evaluate them with precision, and connect with buyers to maximize your ROI.
              </p>
            </div>

            {/* Enhanced CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start animate-slide-up delay-300">
              <Button
                size="lg"
                className="bg-gradient-primary hover:shadow-glow hover:scale-105 transition-all duration-300 group relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                <Star className="mr-2 h-4 w-4 group-hover:rotate-12 transition-transform" />
                Get Started
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="group hover:bg-primary/5 transition-all duration-300 border-2 hover:border-primary/30 hover:shadow-md"
              >
                <Play className="mr-2 h-4 w-4 group-hover:scale-110 transition-transform" />
                Watch Demo
                <div className="ml-2 w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              </Button>
            </div>

            {/* Enhanced Stats */}
            <div className="grid grid-cols-3 gap-4 sm:gap-8 pt-6 lg:pt-8 animate-slide-up delay-400">
              <div className="text-center lg:text-left group cursor-pointer p-4 rounded-xl hover:bg-white/5 transition-all duration-300">
                <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground group-hover:scale-110 transition-transform">
                  50K+
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground group-hover:text-foreground/80 transition-colors">Domains Analyzed</div>
                <div className="w-full h-1 bg-primary/20 rounded-full mt-2 overflow-hidden">
                  <div className="h-full bg-gradient-primary w-0 group-hover:w-full transition-all duration-1000 delay-200"></div>
                </div>
              </div>
              <div className="text-center lg:text-left group cursor-pointer p-4 rounded-xl hover:bg-white/5 transition-all duration-300">
                <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground group-hover:scale-110 transition-transform">
                  95%
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground group-hover:text-foreground/80 transition-colors">AI Accuracy</div>
                <div className="w-full h-1 bg-primary/20 rounded-full mt-2 overflow-hidden">
                  <div className="h-full bg-gradient-primary w-0 group-hover:w-[98%] transition-all duration-1000 delay-200"></div>
                </div>
              </div>
              <div className="text-center lg:text-left group cursor-pointer p-4 rounded-xl hover:bg-white/5 transition-all duration-300">
                <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground group-hover:scale-110 transition-transform">
                  $1.4M
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground group-hover:text-foreground/80 transition-colors">Total Sales</div>
                <div className="w-full h-1 bg-primary/20 rounded-full mt-2 overflow-hidden">
                  <div className="h-full bg-gradient-primary w-0 group-hover:w-full transition-all duration-1000 delay-200"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Hero Image */}
          <div className="relative order-first lg:order-last animate-slide-up delay-500">
            <div className="relative rounded-2xl overflow-hidden shadow-glow group">
              <img
                src={heroImage}
                alt="AI-powered domain analysis dashboard"
                className="w-full h-auto object-cover group-hover:scale-105 transition-transform duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-secondary/10 rounded-2xl opacity-30 group-hover:opacity-10 transition-opacity duration-300"></div>

              {/* Enhanced Floating Elements */}
              <div className="absolute top-4 right-4 bg-white/95 backdrop-blur-md rounded-xl p-3 shadow-xl animate-float border border-green-200">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-600">+25% ROI</span>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                </div>
              </div>

              <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-md rounded-xl p-3 shadow-xl animate-float delay-1000 border border-blue-200">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-600">AI Powered</span>
                  <Sparkles className="h-3 w-3 text-blue-400 animate-pulse" />
                </div>
              </div>

              <div className="absolute top-1/2 right-8 bg-white/95 backdrop-blur-md rounded-xl p-3 shadow-xl animate-float delay-500 border border-purple-200">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium text-purple-600">Secure</span>
                </div>
              </div>
            </div>

            {/* Enhanced Background Decoration */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-primary/30 to-secondary/20 rounded-full blur-xl animate-pulse"></div>
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-tr from-secondary/30 to-primary/20 rounded-full blur-xl animate-pulse delay-500"></div>
            <div className="absolute top-1/2 -left-8 w-16 h-16 bg-accent/20 rounded-full blur-lg animate-pulse delay-1000"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;