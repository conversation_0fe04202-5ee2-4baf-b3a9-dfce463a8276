﻿import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();

  const navItems = [
    { name: "Features", href: "#features" },
    { name: "Pricing", href: "#pricing" },
    { name: "About", href: "#about" }
  ];

  return (
    <header className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-5xl px-4">
      <div style={{ backgroundColor: 'rgba(106, 80, 237, 0.7)' }} className="backdrop-blur-md border border-white/30 rounded-2xl shadow-lg floating-navbar">
        <div className="flex items-center justify-between h-16 px-6">
          {/* Logo */}
          <div className="flex items-center cursor-pointer" onClick={() => navigate("/")}>
            <img
              src="/DomainSpot_logo.svg"
              alt="DomainSpot Logo"
              className="h-20 w-20 object-contain"
              onError={(e) => {
                e.currentTarget.style.display = "none";
              }}
            />
          </div>

          {/* Centered Navigation */}
          <nav className="hidden md:flex items-center justify-center flex-1 space-x-12">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="text-white hover:text-white/80 transition-colors duration-200 font-medium"
              >
                {item.name}
              </a>
            ))}
          </nav>

          {/* Right side buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <Button variant="ghost" className="text-white hover:text-white/80 hover:bg-white/10" onClick={() => navigate("/auth")}>
              Sign In
            </Button>
            <Button className="bg-white text-[#6A50ED] hover:bg-white/90" onClick={() => navigate("/auth")}>
              Get Started
            </Button>
          </div>

          {/* Mobile menu button (no menu icon, just hamburger) */}
          <button
            className="md:hidden p-2 rounded-lg hover:bg-white/10 transition-colors text-white"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </button>
        </div>

        {isMenuOpen && (
          <div className="md:hidden border-t border-white/20 p-4 space-y-4">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="block text-white hover:text-white/80 transition-colors duration-200 font-medium py-2 text-center"
                onClick={() => setIsMenuOpen(false)}
              >
                {item.name}
              </a>
            ))}
            <div className="flex flex-col space-y-2 pt-4 border-t border-white/20">
              <Button variant="ghost" className="w-full text-white hover:text-white/80 hover:bg-white/10" onClick={() => navigate("/auth")}>
                Sign In
              </Button>
              <Button className="w-full bg-white text-[#6A50ED] hover:bg-white/90" onClick={() => navigate("/auth")}>
                Get Started
              </Button>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
