import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import DashboardNavbar from "@/components/dashboard/DashboardNavbar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TrendingUp, TrendingDown, Activity, Zap, Globe, Brain } from "lucide-react";

const MarketTrends = () => {
  const { isAuthenticated } = useAuth();
  const [category, setCategory] = useState("all");
  const [timeframe, setTimeframe] = useState("30d");

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  const trendingKeywords = [
    { keyword: "AI", growth: "+45%", volume: "High", avgPrice: "$12,500" },
    { keyword: "Blockchain", growth: "+32%", volume: "High", avgPrice: "$8,900" },
    { keyword: "Voice", growth: "+28%", volume: "Medium", avgPrice: "$6,200" },
    { keyword: "Crypto", growth: "+25%", volume: "High", avgPrice: "$15,800" },
    { keyword: "Smart", growth: "+22%", volume: "High", avgPrice: "$4,500" },
    { keyword: "Tech", growth: "+18%", volume: "Very High", avgPrice: "$7,300" }
  ];

  const extensionTrends = [
    { extension: ".ai", change: "+38%", avgPrice: "$18,000", status: "hot" },
    { extension: ".io", change: "+15%", avgPrice: "$8,500", status: "stable" },
    { extension: ".tech", change: "+12%", avgPrice: "$3,200", status: "growing" },
    { extension: ".app", change: "+8%", avgPrice: "$4,800", status: "stable" },
    { extension: ".dev", change: "-5%", avgPrice: "$2,100", status: "declining" },
    { extension: ".com", change: "+3%", avgPrice: "$12,000", status: "stable" }
  ];

  const marketInsights = [
    {
      title: "AI Domain Surge",
      description: "AI-related domains are experiencing unprecedented growth",
      impact: "High",
      category: "Technology",
      timeframe: "Next 6 months"
    },
    {
      title: "Short Domain Premium",
      description: "3-4 character domains commanding premium prices",
      impact: "Medium",
      category: "General",
      timeframe: "Ongoing"
    },
    {
      title: "Voice Tech Rising",
      description: "Voice and audio technology domains gaining traction",
      impact: "Medium",
      category: "Technology",
      timeframe: "Next 3 months"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "hot": return "bg-red-100 text-red-800 border-red-200";
      case "growing": return "bg-green-100 text-green-800 border-green-200";
      case "stable": return "bg-blue-100 text-blue-800 border-blue-200";
      case "declining": return "bg-gray-100 text-gray-800 border-gray-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case "High": return "bg-red-100 text-red-800 border-red-200";
      case "Medium": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Low": return "bg-green-100 text-green-800 border-green-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col">
          <DashboardNavbar
            title="Market Trends"
            subtitle="Real-time domain market insights"
          />

          <main className="flex-1 container mx-auto px-6 py-8 space-y-8">
            {/* Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold flex items-center gap-2">
                  <Activity className="h-8 w-8 text-primary" />
                  Market Trends
                </h1>
                <p className="text-muted-foreground">
                  Stay ahead of the market with real-time domain trends and insights
                </p>
              </div>
              <div className="flex gap-3">
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="tech">Technology</SelectItem>
                    <SelectItem value="finance">Finance</SelectItem>
                    <SelectItem value="health">Health</SelectItem>
                    <SelectItem value="ecommerce">E-commerce</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={timeframe} onValueChange={setTimeframe}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7d">7 days</SelectItem>
                    <SelectItem value="30d">30 days</SelectItem>
                    <SelectItem value="90d">90 days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Market Summary */}
            <div className="grid md:grid-cols-4 gap-6">
              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Market Activity</p>
                      <p className="text-2xl font-bold">High</p>
                    </div>
                    <Zap className="h-8 w-8 text-yellow-600" />
                  </div>
                  <div className="flex items-center gap-1 mt-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-green-600">+15% vs last month</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Avg Sale Price</p>
                      <p className="text-2xl font-bold">$8,750</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-green-600" />
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">+12% this month</p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Hot Categories</p>
                      <p className="text-2xl font-bold">6</p>
                    </div>
                    <Brain className="h-8 w-8 text-primary" />
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">AI leads growth</p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">New Extensions</p>
                      <p className="text-2xl font-bold">12</p>
                    </div>
                    <Globe className="h-8 w-8 text-blue-600" />
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">This quarter</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid lg:grid-cols-2 gap-8">
              {/* Trending Keywords */}
              <Card className="border-0 shadow-soft">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Trending Keywords
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {trendingKeywords.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                        <div className="flex items-center gap-3">
                          <Badge variant="outline" className="text-green-600 border-green-600">
                            {item.growth}
                          </Badge>
                          <div>
                            <p className="font-medium">{item.keyword}</p>
                            <p className="text-sm text-muted-foreground">
                              Volume: {item.volume}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold">{item.avgPrice}</p>
                          <p className="text-xs text-muted-foreground">Avg price</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Extension Trends */}
              <Card className="border-0 shadow-soft">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    Extension Performance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {extensionTrends.map((ext, index) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                        <div className="flex items-center gap-3">
                          <Badge className={getStatusColor(ext.status)} variant="outline">
                            {ext.status}
                          </Badge>
                          <div>
                            <p className="font-medium">{ext.extension}</p>
                            <div className="flex items-center gap-1">
                              {ext.change.startsWith('+') ? (
                                <TrendingUp className="h-3 w-3 text-green-600" />
                              ) : (
                                <TrendingDown className="h-3 w-3 text-red-600" />
                              )}
                              <span className={`text-sm ${ext.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                                {ext.change}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold">{ext.avgPrice}</p>
                          <p className="text-xs text-muted-foreground">Avg price</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Market Insights */}
            <Card className="border-0 shadow-soft">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  AI Market Insights
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-6">
                  {marketInsights.map((insight, index) => (
                    <div key={index} className="p-4 rounded-lg border bg-card space-y-3">
                      <div className="flex items-start justify-between">
                        <h3 className="font-semibold">{insight.title}</h3>
                        <Badge className={getImpactColor(insight.impact)} variant="outline">
                          {insight.impact}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{insight.description}</p>
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-muted-foreground">{insight.category}</span>
                        <span className="text-muted-foreground">{insight.timeframe}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default MarketTrends;