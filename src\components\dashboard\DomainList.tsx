import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, ExternalLink, TrendingUp } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Domain {
  id: string;
  name: string;
  purchasePrice: number;
  currentValue: number;
  status: string;
  score: number;
  purchaseDate: string;
  category: string;
  traffic: number;
  offers: Array<{
    id: string;
    amount: number;
    status: string;
    date: string;
  }>;
  salePrice?: number;
  saleDate?: string;
}

interface DomainListProps {
  domains: Domain[];
}

const DomainList = ({ domains }: DomainListProps) => {
  const getStatusBadge = (status: string) => {
    const styles = {
      active: "bg-green-100 text-green-800",
      for_sale: "bg-blue-100 text-blue-800",
      sold: "bg-gray-100 text-gray-800"
    };
    
    return (
      <Badge variant="outline" className={styles[status as keyof typeof styles]}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 80) return "text-blue-600";
    if (score >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <Card className="border-0 shadow-soft">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Domain Portfolio
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {domains.map((domain) => (
            <div
              key={domain.id}
              className="flex items-center justify-between p-4 border border-border rounded-lg hover:shadow-soft transition-all"
            >
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-semibold text-lg">{domain.name}</h3>
                    {getStatusBadge(domain.status)}
                    <span className={`font-bold ${getScoreColor(domain.score)}`}>
                      {domain.score}
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>Purchased: ${domain.purchasePrice.toLocaleString()}</span>
                    <span>•</span>
                    <span>Category: {domain.category}</span>
                    <span>•</span>
                    <span>Traffic: {domain.traffic}/mo</span>
                    {domain.offers.length > 0 && (
                      <>
                        <span>•</span>
                        <span className="text-primary font-medium">
                          {domain.offers.filter(o => o.status === 'pending').length} pending offers
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="font-semibold text-lg">
                    ${domain.status === 'sold' ? domain.salePrice?.toLocaleString() : domain.currentValue.toLocaleString()}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {domain.status === 'sold' ? 'Sale Price' : 'Current Value'}
                  </div>
                  {domain.status !== 'sold' && (
                    <div className="text-xs text-green-600 font-medium">
                      +{Math.round(((domain.currentValue - domain.purchasePrice) / domain.purchasePrice) * 100)}%
                    </div>
                  )}
                </div>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <ExternalLink className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                    {domain.status === 'active' && (
                      <DropdownMenuItem>
                        List for Sale
                      </DropdownMenuItem>
                    )}
                    {domain.offers.length > 0 && (
                      <DropdownMenuItem>
                        View Offers ({domain.offers.length})
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem>
                      Download Report
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default DomainList;