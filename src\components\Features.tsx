import { Card, CardContent } from "@/components/ui/card";
import { Search, Brain, Handshake } from "lucide-react";

const Features = () => {
  const features = [
    {
      icon: <Search className="h-8 w-8" style={{ color: '#6A50ED' }} />,
      title: "Smart Discovery",
      description: "AI-powered algorithms scan millions of expiring domains and identify hidden gems with high investment potential.",
      details: [
        "Real-time monitoring of domain marketplaces",
        "Advanced filtering by industry and keywords",
        "Trending domain pattern analysis"
      ]
    },
    {
      icon: <Brain className="h-8 w-8" style={{ color: '#6A50ED' }} />,
      title: "AI Evaluation",
      description: "Get instant, accurate valuations using machine learning models trained on millions of domain sales.",
      details: [
        "Market trend analysis and price predictions",
        "SEO value and traffic potential scoring",
        "Brand value and memorability assessment"
      ]
    },
    {
      icon: <Handshake className="h-8 w-8" style={{ color: '#6A50ED' }} />,
      title: "Buyer Matching",
      description: "Connect with the right buyers through our intelligent matching system and automated outreach tools.",
      details: [
        "AI-powered buyer identification",
        "Automated personalized outreach campaigns",
        "Negotiation support and deal tracking"
      ]
    }
  ];

  return (
    <section id="features" className="py-16 lg:py-24 bg-gradient-to-br from-slate-100 to-slate-200 relative overflow-hidden">
      {/* Background Decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-purple-200/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-blue-200/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="text-center space-y-6 mb-16">
          <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-100 to-blue-100 rounded-full border border-purple-200 shadow-sm">
            <Brain className="h-4 w-4" style={{ color: '#6A50ED' }} />
            <span className="text-sm font-medium" style={{ color: '#6A50ED' }}>AI-Powered Features</span>
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold leading-tight text-gray-900">
            Everything You Need to
            <span className="block sm:inline"> Make a Profit from Selling Domains</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            From discovery to sale, our comprehensive platform handles every aspect of domain investing with AI precision and market intelligence.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="group hover:shadow-xl hover:scale-105 transition-all duration-300 border border-gray-200 bg-white hover:border-purple-300 rounded-2xl overflow-hidden"
            >
              <CardContent className="p-6 space-y-4">
                <div className="w-16 h-16 rounded-xl bg-gradient-to-br from-purple-50 to-blue-50 flex items-center justify-center border border-purple-100 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>

                <div className="space-y-3">
                  <h3 className="text-lg font-bold text-gray-900 group-hover:text-purple-700 transition-colors duration-300">{feature.title}</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>

                  <ul className="space-y-2">
                    {feature.details.map((detail, idx) => (
                      <li key={idx} className="flex items-start gap-2 text-xs text-gray-500">
                        <div className="w-1.5 h-1.5 rounded-full bg-purple-400 mt-1.5 flex-shrink-0"></div>
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;