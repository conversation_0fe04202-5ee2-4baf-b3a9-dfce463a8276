import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON>, CheckCircle } from "lucide-react";

const CTA = () => {
  const benefits = [
    "14-day free trial",
    "No credit card required",
    "Cancel anytime",
    "Full platform access"
  ];

  return (
    <section className="py-8 bg-gradient-primary relative overflow-hidden">
      {/* Enhanced background patterns */}
      <div className="absolute inset-0 opacity-50">
        <div className="w-full h-full bg-white/5 bg-[radial-gradient(circle_at_center,white_1px,transparent_1px)] bg-[length:60px_60px]"></div>
      </div>

      {/* Additional decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-10 left-10 w-8 h-8 bg-white/10 rounded-full animate-float"></div>
        <div className="absolute bottom-20 right-20 w-6 h-6 bg-white/15 rounded-full animate-float delay-700"></div>
        <div className="absolute top-1/3 right-10 w-4 h-4 bg-white/20 rounded-full animate-float delay-1500"></div>
        <div className="absolute bottom-10 left-1/4 w-5 h-5 bg-white/12 rounded-full animate-float delay-300"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-3xl mx-auto text-center space-y-6">
          <div className="space-y-3">
            <h2 className="text-2xl lg:text-3xl font-bold text-primary-foreground">
              Ready to Transform Your Domain Investing?
            </h2>
            <p className="text-lg text-primary-foreground/80 max-w-xl mx-auto">
              Join thousands of investors already using AI to discover, evaluate,
              and sell domains more profitably than ever before.
            </p>
          </div>

          <div className="flex justify-center">
            <Button
              size="lg"
              variant="secondary"
              className="bg-white text-primary hover:bg-white/90 shadow-soft hover:shadow-xl hover:scale-105 transition-all duration-300 border-2 border-transparent hover:border-primary/30 hover:border-opacity-50 animate-pulse hover:animate-none"
            >
              Join 8000+ Investors
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>

          <div className="flex flex-wrap justify-center gap-6 pt-2">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center gap-2 text-primary-foreground/80">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm">{benefit}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTA;