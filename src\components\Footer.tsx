import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Mail, Twitter, Linkedin, Github, Globe, Sparkles } from "lucide-react";

const Footer = () => {
  const footerLinks = {
    company: [
      { name: "About", href: "#about" },
      { name: "Blog", href: "#blog" },
      { name: "Contact", href: "#contact" }
    ],
    legal: [
      { name: "Privacy Policy", href: "#privacy" },
      { name: "Terms of Service", href: "#terms" }
    ]
  };

  return (
    <footer style={{ backgroundColor: '#6A50ED' }} className="border-t relative overflow-hidden">
      {/* Background Decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-1/4 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        {/* Links Section */}
        <div className="py-12 grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
          {/* Newsletter Card */}
          <div className="md:col-span-1 bg-white/10 backdrop-blur-sm rounded-2xl p-5 border border-white/20">
            <div className="space-y-3">
              <div className="inline-flex items-center gap-2 px-3 py-1 bg-white/10 rounded-full border border-white/20">
                <Sparkles className="h-3 w-3 text-white" />
                <span className="text-xs font-medium text-white">Newsletter</span>
              </div>
              <h4 className="text-base font-bold text-white">
                Never Miss a Domain Opportunity
              </h4>
              <p className="text-sm text-white/80">
                Get weekly domain insights and market analysis.
              </p>
              <div className="space-y-2">
                <Input
                  type="email"
                  placeholder="Your email"
                  className="h-9 bg-white/10 backdrop-blur-sm border-white/20 focus:border-white/40 text-white placeholder:text-white/60"
                />
                <Button className="w-full bg-white text-[#6A50ED] hover:bg-white/90 transition-all duration-300 h-9">
                  <Mail className="mr-2 h-3 w-3" />
                  Subscribe
                </Button>
              </div>
            </div>
          </div>

          {/* Company Links */}
          <div className="space-y-4">
            <h4 className="font-semibold text-white text-base">Company</h4>
            <ul className="space-y-3">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-white/80 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Links */}
          <div className="space-y-4">
            <h4 className="font-semibold text-white text-base">Legal</h4>
            <ul className="space-y-3">
              {footerLinks.legal.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-white/80 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="py-6 border-t border-white/20">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-4">
            <div className="flex flex-col sm:flex-row items-center gap-4">
              {/* Logo */}
              <div className="flex items-center gap-3">
                <img
                  src="/DomainSpot_logo.svg"
                  alt="DomainSpot Logo"
                  className="h-10 w-10 object-contain"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              </div>

              <div className="text-white/80 text-sm text-center sm:text-left">
                © 2025 DomainSpot. All rights reserved. | Empowering domain investors worldwide.
              </div>
            </div>

            <div className="flex items-center gap-3">
              <span className="text-sm text-white/80 hidden sm:block">Follow us:</span>
              <div className="flex items-center gap-2">
                <a
                  href="#"
                  className="p-2 rounded-full bg-white/10 text-white/80 hover:text-white hover:bg-white/20 transition-all duration-300 hover:scale-110"
                  aria-label="Twitter"
                >
                  <Twitter className="h-3 w-3" />
                </a>
                <a
                  href="#"
                  className="p-2 rounded-full bg-white/10 text-white/80 hover:text-white hover:bg-white/20 transition-all duration-300 hover:scale-110"
                  aria-label="LinkedIn"
                >
                  <Linkedin className="h-3 w-3" />
                </a>
                <a
                  href="#"
                  className="p-2 rounded-full bg-white/10 text-white/80 hover:text-white hover:bg-white/20 transition-all duration-300 hover:scale-110"
                  aria-label="GitHub"
                >
                  <Github className="h-3 w-3" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;