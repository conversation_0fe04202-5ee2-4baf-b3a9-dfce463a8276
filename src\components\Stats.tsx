import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Users, Target, TrendingUp, Award, ChevronLeft, ChevronRight, Star } from "lucide-react";
import { useState, useEffect } from "react";

const Stats = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isPaused, setIsPaused] = useState(false);

  const stats = [
    {
      icon: <Users className="h-8 w-8 text-primary" />,
      number: "10,000+",
      label: "Active Investors",
      description: "Join a thriving community of domain investors"
    },
    {
      icon: <Target className="h-8 w-8 text-primary" />,
      number: "95%",
      label: "Success Rate",
      description: "Of domains sold within 90 days using our platform"
    },
    {
      icon: <TrendingUp className="h-8 w-8 text-primary" />,
      number: "340%",
      label: "Average ROI",
      description: "Return on investment for our top performers"
    },
    {
      icon: <Award className="h-8 w-8 text-primary" />,
      number: "$50M+",
      label: "Total Sales",
      description: "Facilitated through our marketplace"
    }
  ];

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Domain Investor",
      company: "Chen Ventures",
      content: "DomainSpot's AI helped me identify a domain that sold for 400% profit within 3 months. The evaluation accuracy is incredible.",
      rating: 5
    },
    {
      name: "Michael Rodriguez",
      role: "Portfolio Manager",
      company: "Digital Assets Co",
      content: "The platform's market insights saved me from bad investments and guided me to profitable opportunities I would have missed.",
      rating: 5
    },
    {
      name: "Emily Johnson",
      role: "Entrepreneur",
      company: "StartupDomains",
      content: "As a beginner, DomainSpot's AI guidance was invaluable. I've built a profitable portfolio in just 6 months.",
      rating: 5
    },
    {
      name: "David Park",
      role: "Investment Advisor",
      company: "Park Capital",
      content: "The ROI tracking and analytics features help me make data-driven decisions. My success rate has improved by 60%.",
      rating: 5
    },
    {
      name: "Lisa Thompson",
      role: "Domain Broker",
      company: "Premium Domains LLC",
      content: "DomainSpot's marketplace connections have expanded my network significantly. The platform is a game-changer.",
      rating: 5
    }
  ];

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  // Auto-play functionality for testimonials
  useEffect(() => {
    if (!isPaused) {
      const interval = setInterval(() => {
        nextTestimonial();
      }, 4000); // Change testimonial every 4 seconds

      return () => clearInterval(interval);
    }
  }, [isPaused]);

  return (
    <section className="py-20 bg-gradient-hero">
      <div className="container mx-auto px-6">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl lg:text-5xl font-bold">
            Trusted by
            <span className="text-foreground"> Domain Investors</span>
            <br />Worldwide
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Join thousands of successful domain investors who trust our AI-powered platform
            to maximize their returns.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Stats Section - Left Side */}
          <div className="space-y-8">
            <div className="grid grid-cols-2 gap-6">
              {stats.map((stat, index) => (
                <Card
                  key={index}
                  className="group hover:shadow-soft hover:scale-105 transition-all duration-500 border-0 bg-card/50 backdrop-blur cursor-pointer animate-fade-in"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <CardContent className="p-6 text-center space-y-4">
                    <div className="w-16 h-16 rounded-xl bg-gradient-secondary mx-auto flex items-center justify-center group-hover:scale-125 group-hover:rotate-12 transition-all duration-500">
                      {stat.icon}
                    </div>
                    <div className="space-y-2">
                      <div className="text-3xl font-bold text-foreground group-hover:scale-110 transition-transform duration-300 inline-block">
                        {stat.number}
                      </div>
                      <div className="font-semibold text-foreground group-hover:text-primary transition-colors duration-300">{stat.label}</div>
                      <p className="text-sm text-muted-foreground group-hover:text-foreground/70 transition-colors duration-300">{stat.description}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Testimonials Section - Right Side */}
          <div
            className="space-y-6"
            onMouseEnter={() => setIsPaused(true)}
            onMouseLeave={() => setIsPaused(false)}
          >
            <div className="text-center lg:text-left">
              <h3 className="text-2xl font-bold text-foreground mb-2">What Our Investors Say</h3>
              <p className="text-muted-foreground">Real success stories from our community</p>
            </div>

            <Card className="bg-card/50 backdrop-blur border-0 shadow-soft">
              <CardContent className="p-8">
                <div className="space-y-6">
                  <div className="flex items-center gap-1">
                    {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 fill-primary text-primary" />
                    ))}
                  </div>

                  <blockquote className="text-lg text-foreground leading-relaxed">
                    "{testimonials[currentTestimonial].content}"
                  </blockquote>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-semibold text-foreground">{testimonials[currentTestimonial].name}</div>
                      <div className="text-sm text-muted-foreground">
                        {testimonials[currentTestimonial].role} at {testimonials[currentTestimonial].company}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Navigation Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentTestimonial(index)}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${index === currentTestimonial ? 'bg-primary w-8' : 'bg-muted-foreground/30'
                      }`}
                  />
                ))}
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={prevTestimonial}
                  className="h-8 w-8 p-0"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={nextTestimonial}
                  className="h-8 w-8 p-0"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Stats;