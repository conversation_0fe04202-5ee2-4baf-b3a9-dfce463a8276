import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import DashboardNavbar from "@/components/dashboard/DashboardNavbar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, BarChart3, PieChart, Activity, DollarSign } from "lucide-react";

const Analytics = () => {
  const { isAuthenticated } = useAuth();
  const [timeRange, setTimeRange] = useState("30d");

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  const analyticsData = {
    totalValue: "$124,500",
    totalDomains: 45,
    avgValue: "$2,767",
    growth: "+12.5%",
    topPerformers: [
      { domain: "techstartup.ai", value: "$15,000", change: "+25%" },
      { domain: "blockchain-tools.com", value: "$12,800", change: "+18%" },
      { domain: "aivoice.io", value: "$9,500", change: "+32%" }
    ],
    categories: [
      { name: "AI/Tech", percentage: 35, value: "$43,575" },
      { name: "E-commerce", percentage: 25, value: "$31,125" },
      { name: "Finance", percentage: 20, value: "$24,900" },
      { name: "Health", percentage: 15, value: "$18,675" },
      { name: "Other", percentage: 5, value: "$6,225" }
    ]
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col">
          <DashboardNavbar
            title="Analytics"
            subtitle="Portfolio performance insights"
          />

          <main className="flex-1 container mx-auto px-6 py-8 space-y-8">
            {/* Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold">Portfolio Analytics</h1>
                <p className="text-muted-foreground">
                  Detailed insights and performance metrics for your domain portfolio
                </p>
              </div>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Key Metrics */}
            <div className="grid md:grid-cols-4 gap-6">
              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Total Portfolio Value</p>
                      <p className="text-2xl font-bold">{analyticsData.totalValue}</p>
                    </div>
                    <DollarSign className="h-8 w-8 text-primary" />
                  </div>
                  <div className="flex items-center gap-1 mt-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-green-600">{analyticsData.growth}</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Total Domains</p>
                      <p className="text-2xl font-bold">{analyticsData.totalDomains}</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-primary" />
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">Across all categories</p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Average Value</p>
                      <p className="text-2xl font-bold">{analyticsData.avgValue}</p>
                    </div>
                    <Activity className="h-8 w-8 text-primary" />
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">Per domain</p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-soft">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Growth Rate</p>
                      <p className="text-2xl font-bold text-green-600">{analyticsData.growth}</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-green-600" />
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">Last 30 days</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid lg:grid-cols-2 gap-8">
              {/* Top Performers */}
              <Card className="border-0 shadow-soft">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Top Performers
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {analyticsData.topPerformers.map((domain, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                      <div>
                        <p className="font-medium">{domain.domain}</p>
                        <p className="text-sm text-muted-foreground">Current value</p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{domain.value}</p>
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          {domain.change}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Category Breakdown */}
              <Card className="border-0 shadow-soft">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChart className="h-5 w-5" />
                    Portfolio Breakdown
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {analyticsData.categories.map((category, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{category.name}</span>
                        <div className="text-right">
                          <span className="text-sm font-bold">{category.value}</span>
                          <span className="text-xs text-muted-foreground ml-2">({category.percentage}%)</span>
                        </div>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className="bg-gradient-primary h-2 rounded-full transition-all duration-1000"
                          style={{ width: `${category.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* Performance Chart Placeholder */}
            <Card className="border-0 shadow-soft">
              <CardHeader>
                <CardTitle>Portfolio Performance Over Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center bg-muted/20 rounded-lg">
                  <div className="text-center space-y-2">
                    <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto" />
                    <p className="text-muted-foreground">Interactive chart coming soon</p>
                    <Button variant="outline" size="sm">
                      Export Data
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Analytics;