import { useEffect, useState } from "react";

const Partners = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Partner logos - using placeholder URLs for now, replace with actual partner logos
  const partners = [
    {
      name: "GoDaddy",
      logo: "https://img.logo.dev/godaddy.com?token=pk_X-1ZO13GSVKaE1hf9NbWdQ&size=200",
      alt: "GoDaddy Logo"
    },
    {
      name: "Namecheap",
      logo: "https://img.logo.dev/namecheap.com?token=pk_X-1ZO13GSVKaE1hf9NbWdQ&size=200",
      alt: "Namecheap Logo"
    },
    {
      name: "Domain.com",
      logo: "https://img.logo.dev/domain.com?token=pk_X-1ZO13GSVKaE1hf9NbWdQ&size=200",
      alt: "Domain.com Logo"
    },
    {
      name: "Se<PERSON>",
      logo: "https://img.logo.dev/sedo.com?token=pk_X-1ZO13GSVKaE1hf9NbWdQ&size=200",
      alt: "Sedo Logo"
    },
    {
      name: "<PERSON>lip<PERSON>",
      logo: "https://img.logo.dev/flippa.com?token=pk_X-1ZO13GSVKaE1hf9NbWdQ&size=200",
      alt: "Flippa Logo"
    },
    {
      name: "Dynadot",
      logo: "https://img.logo.dev/dynadot.com?token=pk_X-1ZO13GSVKaE1hf9NbWdQ&size=200",
      alt: "Dynadot Logo"
    },
    {
      name: "Porkbun",
      logo: "https://img.logo.dev/porkbun.com?token=pk_X-1ZO13GSVKaE1hf9NbWdQ&size=200",
      alt: "Porkbun Logo"
    },
    {
      name: "Name.com",
      logo: "https://img.logo.dev/name.com?token=pk_X-1ZO13GSVKaE1hf9NbWdQ&size=200",
      alt: "Name.com Logo"
    }
  ];

  // Auto-scroll functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % partners.length);
    }, 3000); // Change every 3 seconds

    return () => clearInterval(interval);
  }, [partners.length]);

  // Calculate visible partners (show 4 at a time on desktop, 2 on mobile)
  const getVisiblePartners = () => {
    const visibleCount = window.innerWidth >= 768 ? 4 : 2;
    const visible = [];
    for (let i = 0; i < visibleCount; i++) {
      const index = (currentIndex + i) % partners.length;
      visible.push(partners[index]);
    }
    return visible;
  };

  const [visiblePartners, setVisiblePartners] = useState(getVisiblePartners());

  useEffect(() => {
    setVisiblePartners(getVisiblePartners());
  }, [currentIndex]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setVisiblePartners(getVisiblePartners());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [currentIndex]);

  return (
    <section className="py-16 bg-background border-t border-border/50">
      <div className="container mx-auto px-6">
        <div className="text-center space-y-4 mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-foreground">
            Trusted Partners
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            We work with leading domain marketplaces and registrars to bring you the best opportunities
          </p>
        </div>

        {/* Partners Logo Carousel */}
        <div className="relative overflow-hidden">
          <div className="flex items-center justify-center gap-8 md:gap-12 transition-all duration-500 ease-in-out">
            {visiblePartners.map((partner, index) => (
              <div
                key={`${partner.name}-${index}`}
                className="flex-shrink-0 group cursor-pointer"
              >
                <div className="w-32 h-20 md:w-40 md:h-24 flex items-center justify-center p-4 rounded-lg bg-card/50 backdrop-blur-sm border border-border/30 hover:border-primary/30 transition-all duration-300 hover:shadow-soft group-hover:scale-105">
                  <img
                    src={partner.logo}
                    alt={partner.alt}
                    className="max-w-full max-h-full object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300 opacity-70 group-hover:opacity-100"
                    onError={(e) => {
                      // Fallback to text if image fails to load
                      e.currentTarget.style.display = 'none';
                      const textElement = e.currentTarget.nextElementSibling as HTMLElement;
                      if (textElement) {
                        textElement.style.display = 'block';
                      }
                    }}
                  />
                  <span 
                    className="hidden text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors duration-300"
                    style={{ display: 'none' }}
                  >
                    {partner.name}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Progress indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {partners.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-primary w-6'
                    : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
                }`}
                onClick={() => setCurrentIndex(index)}
              />
            ))}
          </div>
        </div>

        {/* Additional info */}
        <div className="text-center mt-12">
          <p className="text-sm text-muted-foreground">
            Connecting you with <span className="font-semibold text-foreground">8+ major domain platforms</span> for seamless buying and selling
          </p>
        </div>
      </div>
    </section>
  );
};

export default Partners;
