import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { DollarSign, TrendingUp, Eye, Target } from "lucide-react";

interface StatsCardsProps {
  analytics: {
    totalInvested: number;
    currentValue: number;
    totalProfit: number;
    roi: number;
    activeDomains: number;
    soldDomains: number;
    pendingOffers: number;
    monthlyTraffic: number;
  };
}

const StatsCards = ({ analytics }: StatsCardsProps) => {
  const stats = [
    {
      title: "Portfolio Value",
      value: `$${analytics.currentValue.toLocaleString()}`,
      icon: DollarSign,
      change: "+12.5%",
      description: "vs last month"
    },
    {
      title: "Total ROI",
      value: `${analytics.roi}%`,
      icon: TrendingUp,
      change: "+45.2%",
      description: "overall return"
    },
    {
      title: "Active Domains",
      value: analytics.activeDomains.toString(),
      icon: Target,
      change: `${analytics.soldDomains} sold`,
      description: "this year"
    },
    {
      title: "Monthly Traffic",
      value: `${(analytics.monthlyTraffic / 1000).toFixed(1)}K`,
      icon: Eye,
      change: "+8.2%",
      description: "vs last month"
    }
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <Card key={index} className="border-0 shadow-soft">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {stat.title}
            </CardTitle>
            <stat.icon className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <div className="flex items-center text-xs">
              <span className="text-green-600 font-medium">{stat.change}</span>
              <span className="text-muted-foreground ml-1">{stat.description}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default StatsCards;